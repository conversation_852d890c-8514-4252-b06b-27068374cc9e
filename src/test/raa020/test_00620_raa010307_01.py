import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TEST00620RAA01030701(FukushiSiteTestCaseBase):
    """TEST00620RAA01030701"""

    #ここに操作したコードをコピーして貼り付ける
    def test_00620_raa010307_01(self):
        """test_00620_raa010307_01"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        
        insatsu_tyouhyou_name =  case_data.get("insatsu_tyouhyou_name", "")
        taisho_ym =  case_data.get("taisho_ym", "")
        busyo_bango =  case_data.get("busyo_bango", "")
        sort =  case_data.get("sort", "")

        # ログイン
        self.do_login()
        #1	メインメニュー画面	表示
        self.screen_shot("raa010307-01-01")
        #2	メインメニュー画面	「バッチ起動」ボタン押下
        self.batch_kidou_click()
        #3	バッチ起動画面	表示
        self.screen_shot("raa010307-01-03")
        #4	バッチ起動画面	業務「障害」選択
        self.form_input_by_id(idstr="GyomuSelect", text = "障害")
        #5	バッチ起動画面	事業「療育手帳」選択
        self.form_input_by_id(idstr="JigyoSelect", text = "療育手帳")
        #6	バッチ起動画面	処理区分「月次処理」選択
        self.form_input_by_id(idstr="ShoriKubunSelect", text = "随時処理")
        #7	バッチ起動画面	処理分類「次回再判定申請簿出力処理」選択
        self.form_input_by_id(idstr="ShoriBunruiSelect", text = "帳票出力処理（一括印刷）")

        # 8	バッチ起動画面	「療育手帳再判定のお知らせ出力処理」のNoボタン押下
        self.click_batch_job_button_by_label(insatsu_tyouhyou_name) 
        params = [
            {"title": "対象年月", "type": "text", "value": taisho_ym},
            {"title": "文書番号", "type": "text", "value": busyo_bango},
            {"title": "出力順序", "type": "select", "value": sort}
        ]
        self.set_job_params(params)
        #バッチ起動画面	表示
        self.screen_shot("raa010307-01-10")

        # 11	バッチ起動画面	「処理開始」ボタン押下
        #「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)
        
        #基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("raa010307-01-11")
        # 12	バッチ起動画面	「実行履歴」ボタン押下
        # 「実行履歴」ボタン押下
        self.click_job_exec_log()
        #13	ジョブ実行履歴画面	表示
        self.screen_shot("raa010307-01-13")
        #14	ジョブ実行履歴画面	「検索」ボタン押下
        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(30,20)
        #状態が「正常終了」であることを確認
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        #15	ジョブ実行履歴画面	表示
        self.screen_shot("raa010307-01-15")
        #16	ジョブ実行履歴画面	「帳票履歴」ボタン押下
        # 「帳票履歴」ボタン押下
        self.click_report_log()
        #17	ジョブ帳票履歴画面	表示
        self.screen_shot("raa010307-01-17")
        #18	ジョブ帳票履歴画面	「検索」ボタン押下
        # 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="ケース名")
        #19	ジョブ帳票履歴画面	表示
        self.screen_shot("raa010307-01-19")
                
        #「療育手帳再判定のお知らせ_第一区_900通常」帳票出力
        #25	ジョブ帳票履歴画面	「次回再判定申請簿」のNoボタン押下
        # self.click_by_id("Sel1")
        # self.screen_shot("raa010307-01-20")

        #「次回再判定申請簿」帳票出力
        # self.click_by_id("Sel2")
        # self.screen_shot("raa010307-01-21")

        #×ボタン押下でPDFを閉じる
        self.screen_shot("raa010307-01-29")
