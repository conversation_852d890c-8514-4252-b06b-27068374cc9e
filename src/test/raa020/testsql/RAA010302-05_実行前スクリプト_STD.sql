DELETE FROM WR$$JICHITAI_CODE$$QA..QAZ受給状況 WHERE 宛名コード = '$$ATENA_QAA020_SAIKOUHU$$' AND 業務コード = 'QAA020'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAZ福祉世帯 WHERE 本人宛名コード = '$$ATENA_QAA020_SAIKOUHU$$' AND 業務コード = 'QAA020'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA資格履歴 WHERE 宛名コード = '$$ATENA_QAA020_SAIKOUHU$$' AND 業務コード = 'QAA020'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA療育手帳資格内容 WHERE 宛名コード = '$$ATENA_QAA020_SAIKOUHU$$' AND 業務コード = 'QAA020'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA療育手帳判定記録 WHERE 宛名コード = '$$ATENA_QAA020_SAIKOUHU$$' AND 業務コード = 'QAA020'

--QAZ受給状況
INSERT INTO WR$$JICHITAI_CODE$$QA..QAZ受給状況 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,資格取得日,資格喪失日,申請年月日,申請種別,申請理由,進達年月日1,進達判定年月日1,進達結果1,進達年月日2,進達判定年月日2,進達結果2,決定年月日,決定結果,決定理由,進捗状況,業務固有コード1,業務固有コード2,業務固有コード3,複数申請フラグ,職権フラグ,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA020',522,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA020_SAIKOUHU$$',N'20240522',N'99999999',N'20230401',4,1,N'20230401',N'00000000',0,N'00000000',N'00000000',0,N'00000000',0,0,N'0000300002',N'福祉県1030205',N'',N'',0,N'0',N'0',N'9501',N'9501','2024-05-22 15:29:37.233','2024-05-23 11:59:26.010',N'QAZF045 ');

--QAZ福祉世帯
INSERT INTO WR$$JICHITAI_CODE$$QA..QAZ福祉世帯 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,本人宛名コード,福祉世帯員宛名コード,該当日,非該当日,本人から見た続柄,受給者との関係,汎用項目,同居別居コード,旧姓併記有無,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA020',521,0,N'$$JICHITAI_CODE$$',N'00000',N'',N'$$ATENA_QAA020_SAIKOUHU$$',N'$$ATENA_QAA020_SAIKOUHU_HOGOSHA$$',N'20240522',N'99999999',N'0000000006',N'0000000007',N'0000000000',N'0000000000',N'0',N'0',N'9501',N'9501','2024-05-22 15:29:37.200','2024-05-22 15:29:37.200',N'RAAF003'),
	 (N'QAA020',521,0,N'$$JICHITAI_CODE$$',N'00000',N'',N'$$ATENA_QAA020_SAIKOUHU$$',N'$$ATENA_QAA020_SAIKOUHU$$',N'20240522',N'99999999',N'0000000001',N'0000000001',N'0000000000',N'0000000000',N'0',N'0',N'9501',N'9501','2024-05-22 15:29:37.170','2024-05-22 15:29:37.170',N'RAAF003');

--QAA資格履歴
INSERT INTO WR$$JICHITAI_CODE$$QA..QAA資格履歴 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,申請年月日,申請種別,申請理由,申請理由2,申請理由3,申請理由テキスト,申請内容入力日,進達年月日1,進達判定年月日1,進達結果1,進達内容入力日1,進達先機関コード1,進達年月日2,進達判定年月日2,進達結果2,進達内容入力日2,進達先機関コード2,決定年月日,決定結果,決定理由,決定理由テキスト,決定内容入力日,業務固有コード1,業務固有コード2,業務固有コード3,職権フラグ,受付場所コード,担当場所コード,変更日,資格状態コード,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA020',522,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA020_SAIKOUHU$$',N'20230401',4,1,0,0,N'',N'20240522',N'20230401',N'00000000',0,N'20240523',N'',N'00000000',N'00000000',0,N'00000000',N'',N'00000000',0,0,N'',N'00000000',N'福祉県1030205',N'',N'',N'0',N'0000000000',N'0000000000',N'00000000',N'0000000010',N'0',N'9501',N'9501','2024-05-22 15:42:53.357','2024-05-23 11:59:25.980',N'RAAF003'),
	 (N'QAA020',521,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA020_SAIKOUHU$$',N'20240522',1,1,0,0,N'',N'20240522',N'20240522',N'00000000',0,N'20240522',N'',N'00000000',N'00000000',0,N'00000000',N'',N'20240522',1,0,N'',N'20240522',N'福祉県1030205',N'',N'',N'0',N'0000000000',N'0000000000',N'00000000',N'0000000040',N'0',N'9501',N'9501','2024-05-22 15:29:37.123','2024-05-22 15:33:16.490',N'RAAF003');

--QAA療育手帳資格内容
INSERT INTO WR$$JICHITAI_CODE$$QA..[QAA療育手帳資格内容] ([業務コード],[履歴番号],[履歴分類],[自治体コード],[福祉事務所コード],[支所コード],[宛名コード],[手帳所持有無],[手帳記号],[手帳番号],[種別],[総合判定],[療育判定],[事由発生日],[交付年月日],[再交付年月日],[手帳受領日],[返還年月日],[本籍],[旧手帳記号],[旧手帳番号],[手帳郵便番号1],[手帳郵便番号2],[手帳住所1],[手帳住所2],[手帳カナ氏名],[手帳漢字氏名],[判定区分],[判定予約日],[判定予約時],[判定予約分],[判定記録_連番],[次回判定年月],[次回判定年月を定めない],[判定機関コード],[希望手帳様式コード],[手帳交付方法コード],[手帳交付場所コード],[前回判定日],[前回判定機関],[宛先優先度コード],[依頼日],[報告日],[回答日],[決裁日],[指導記録],[初回交付場所],[程度変更状況コード],[次回判定機関コード],[カード登録日],[カード解除日],[カード発行日],[通知発送日],[手帳引渡日],[NHK受信料減免有無],[NHK受信料減免お客様番号],[有料道路減免有無],[手続き有無],[手続き処理済フラグ],[備考],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム]) VALUES
	 (N'QAA020',522,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA020_SAIKOUHU$$',N'1',N'0000099101',N'1030205',N'0000000000',N'0000000001',N'0000000000',N'00000000',N'20240522',N'00000000',N'00000000',N'00000000',N'0000000000',N'0000000000',N'0',N'000',N'0000',N'',N'',N'',N'',N'0000000000',N'00000000',N'00',N'00',0,N'000000',N'0',N'0000000001',N'0000000001',N'0000000001',N'0000000000',N'20240522',N'',N'0000000000',N'00000000',N'00000000',N'00000000',N'00000000',N'',N'',N'0000000000',N'0000000000',N'00000000',N'00000000',N'00000000',N'00000000',N'00000000',N'0',N'',N'0',N'0',N'0',N'',N'0',N'9501',N'9501',N'2024-05-22 15:42:53.357',N'2024-05-23 11:48:31.397',N'RAAF003'), 
	 (N'QAA020',521,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA020_SAIKOUHU$$',N'1',N'0000099101',N'1030205',N'0000000000',N'0000000001',N'0000000000',N'00000000',N'20240522',N'00000000',N'00000000',N'00000000',N'0000000000',N'0000000000',N'0',N'000',N'0000',N'',N'',N'',N'',N'0000000000',N'00000000',N'00',N'00',0,N'000000',N'0',N'0000000001',N'0000000001',N'0000000001',N'0000000000',N'00000000',N'',N'0000000000',N'00000000',N'00000000',N'00000000',N'00000000',N'',N'',N'0000000000',N'0000000000',N'00000000',N'00000000',N'00000000',N'00000000',N'00000000',N'0',N'',N'0',N'0',N'0',N'',N'0',N'9501',N'9501',N'2024-05-22 15:29:37.123',N'2024-05-22 15:33:16.503',N'RAAF003');

--QAA療育手帳判定記録
INSERT INTO WR$$JICHITAI_CODE$$QA..[QAA療育手帳判定記録] ([自治体コード],[福祉事務所コード],[支所コード],[業務コード],[履歴番号],[履歴分類],[宛名コード],[判定依頼日],[判定年月日],[判定結果],[判定理由テキスト],[次回判定年月],[次回判定年月を定めない],[判定機関コード],[障害程度],[ケース番号],[療育判定],[知能指数],[知能指数コード],[判定基準],[診断書有無],[社会生活能力点数],[重症心身障害児判定],[非該当理由],[判定方法コード],[検査日],[検査時間],[検査機関コード],[検査方式コード],[精神疾患の有無],[発達障害の有無],[精神_発達年齢],[その他検査_程度内容],[総合判定_処遇方針の検討の状況],[医師診察日],[医師診察時間],[医師名_氏],[医師名_名],[心理判定員_氏],[心理判定員_名],[判定機関受付日],[判定機関結果送付日],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム]) VALUES
	(N'$$JICHITAI_CODE$$',N'99301',N'',N'QAA020',521,0,N'$$ATENA_QAA020_SAIKOUHU$$',N'00000000',N'20240522',N'0000000000',N'',N'000000',N'0',N'0000000000',N'0000000000',N'',N'0000000000',N'',N'0000000000',N'0000000000',N' ',N'',N' ',N'0000000000',N'0000000000',N'00000000',N'',N'0000000000',N'0000000000',N'0',N'0',N'',N'',N'',N'00000000',N'',N'',N'',N'',N'',N'00000000',N'00000000',N'0',N'9501',N'9501',N'2024-05-22 15:32:29.763',N'2024-05-22 15:32:29.763',N'RAAF004');
	