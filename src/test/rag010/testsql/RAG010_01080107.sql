
DECLARE @cnt INT;
SELECT @cnt = COUNT(*)
FROM [WR$$JICHITAI_CODE$$ZF].[DBO].[SK福祉_情報照会テーブル_年金_更生医療] WHERE 業務コード = 'QAG010' AND 宛名コード = ('00000' + '$$DELETE_ATENA_CODE$$');
DECLARE @filenm NVARCHAR(255);
SELECT @filenm = 送信ファイル名 
FROM [WR$$JICHITAI_CODE$$ZF].[DBO].[SK福祉_情報照会テーブル_年金_更生医療] WHERE 業務コード = 'QAG010' AND 宛名コード = ('00000' + '$$DELETE_ATENA_CODE$$') order by 送信ファイル名 ; 
DECLARE @filenm1 NVARCHAR(255);
SELECT @filenm1  =  SUBSTRING(@filenm,0,  LEN(@filenm)-3) + '-20171204111500-result.xml';
DECLARE @i INT = 0;
WHILE @i <= @cnt
BEGIN
	DECLARE @renban INT;
	SELECT @renban = 連番 FROM WR$$JICHITAI_CODE$$ZF.dbo.SK福祉_情報照会テーブル_年金_更生医療 WHERE 業務コード = 'QAG010' AND 宛名コード = ('00000' + '$$DELETE_ATENA_CODE$$') ORDER BY 連番 OFFSET @i ROWS FETCH NEXT 1 ROWS ONLY;
	UPDATE WR$$JICHITAI_CODE$$ZF.dbo.SK福祉_情報照会テーブル_年金_更生医療 
    SET 副本登録状況コード = '04000',受信ファイル名 =  @filenm1,
    中間サーバー受付番号 = CONCAT('000000000000000000000',CAST(@i+1 AS VARCHAR)),処理結果区分 = '1',処理結果詳細コード = '000000',
    処理結果メッセージ = '正常終了',中間サーバー受付明細番号 = CONCAT('000000000',CAST(@i+1 AS VARCHAR)),明細単位照会ステータス='09',
    照会処理結果メッセージ='正常終了',特定個人情報名単位照会ステータス = '01',特定個人情報名単位照会処理結果メッセージ = '正常終了',
    提供XMLデータ情報 = '<SpecificPersonalInformation xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
   <DataProperties>
      <TimeAndDateOfRegistration>2017-01-01T00:00:00</TimeAndDateOfRegistration>
      <Version>5</Version>
   </DataProperties>
   <TM00000000000052>
      <TK00005200000001 DateOfDetection="2017-01-02" Version="1">
         <TK00005200000010 DateOfDetection="2017-01-03" Version="2">
            <TK00005200000030 DateOfDetection="2017-01-04" Version="2">
               <TK00005200000031 Version="3">2010-02-01</TK00005200000031>
               <TK00005200000040 Version="3">2010-12-31</TK00005200000040>
            </TK00005200000030>
            <TK00005200000050 DateOfDetection="2017-01-05" Version="1">
               <TK00005200025080 Version="3">2010-01-01</TK00005200025080>
               <TK00005200025090 Version="3">2010-01-31</TK00005200025090>
            </TK00005200000050>
            <TK00005200000050 DateOfDetection="2017-01-06" Version="1">
               <TK00005200025080 Version="3">2012-01-01</TK00005200025080>
               <TK00005200025090 Version="3" ReasonOfNull="Commission"></TK00005200025090>
            </TK00005200000050>
         </TK00005200000010>
      </TK00005200000001>
   </TM00000000000052>
</SpecificPersonalInformation>' WHERE 連番 = @renban;
	SET @i = @i + 1;
END

INSERT INTO [WR$$JICHITAI_CODE$$ZF].[DBO].[受信ファイル管理]([送信ファイル名],[受信ファイル名],[特定個人情報の版番号],[処理状況コード]) VALUES(@filenm,@filenm1,'150','01010');
UPDATE [WR$$JICHITAI_CODE$$ZF].[DBO].[処理ファイル管理] SET 受信ファイル名 = @filenm1,処理状況コード = '01010';

