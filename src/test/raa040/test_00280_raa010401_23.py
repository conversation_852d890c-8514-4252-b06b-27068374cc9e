import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01040123(FukushiSiteTestCaseBase):
    """TESTRAA01040123"""

    def test_case_raa010401_23(self):
        """test_case_raa010401_23"""

        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")
        hantei_data = self.common_test_data.get("TESTRAA01040119", {})
        # 希望手帳様式
        kibou_techou_youshiki = hantei_data.get("kibou_techou_youshiki", "")
        # 手帳交付方法
        techou_kouhu_houhou = hantei_data.get("techou_kouhu_houhou", "")

        if techou_kouhu_houhou == "郵送":
            return

        # ログイン
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA040")

        if self.exist_item(item_type="button", item_id="CmdButton2_1"):
            self.click_by_id("CmdButton2_1")

        # 印刷
        self.click_button_by_label("印刷")
        self.screen_shot("raa010401_23-02")

        # 「障害者手帳交付（再交付）について」の行の数字ボタン押下
        report_param_list = [
            {
                "report_name": insatsu_tyouhyou_name,
                "params": [
                    {"title": "発行年月日", "type": "text", "value": hakkou_ymd}
                ]
            }
        ]
        self.print_online_reports(case_name="ケース名", report_param_list=report_param_list)
        self.assert_message_area("プレビューを表示しました")
        self.screen_shot("raa010401_23-06")

        # 帳票（PDF）表示

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        self.screen_shot("raa010401_23-09")
