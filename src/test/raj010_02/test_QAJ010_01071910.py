import time
import datetime
from datetime import timedelta
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01071910(FukushiSiteTestCaseBase):
    """TestQAJ010_01071910"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01071910"]
        #sql_params = {"ATENA_CODE": case_data.get("atena_code", "")}
        sql_params = {"SEIKYU_YM": case_data.get("seikyu_ym", "")}
        self.exec_sqlfile("TestQAJ010_01071910.sql", params=sql_params)
        super().setUp()
    
    # 取込済相談支援給付費請求書情報を削除できることを確認する。
    def test_QAJ010_01071910(self):
        """取込済相談支援給付費請求書情報削除処理"""

        #date = datetime.date.today()
        #today = format(date, '%Y%m%d')

        case_data = self.test_data["TestQAJ010_01071910"]
        #atena_code = case_data.get("atena_code", "")
        seikyu_ym = case_data.get("seikyu_ym", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")
        
        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")
        
        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")
        
        # 4 バッチ起動画面: 業務「障害」選択
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        
        # 5 バッチ起動画面: 事業「障害者総合支援」選択
        self.form_input_by_id(idstr="JigyoSelect", text="障害者総合支援")
        
        # 6 バッチ起動画面: 処理区分「国保連連携　支払業務」選択
        self.form_input_by_id(idstr="ShoriKubunSelect", text="国保連連携　支払業務")
        
        # 7 バッチ起動画面: 処理分類「取込済一次審査用資料情報削除処理」選択
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="取込済一次審査用資料情報削除処理")
        
        # 8 バッチ起動画面: 取込済相談支援給付費請求書情報削除処理　ボタン押下
        self.find_element(By.ID,"Sel3").click()
        self.screen_shot("バッチ起動画面_8")
        
        # 9 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_9")
        
        # 10 バッチ起動画面: 「請求年月」入力
        # ジョブパラ定義
        params = [
            {"title":"請求年月", "type": "text", "value": seikyu_ym}
        ]
        # ジョブパラに入力
        self.set_job_params(params)
        self.form_input_by_id(idstr="UQZGC402_PrintSelect", text="ファイルアウト")
        self.form_input_by_id(idstr="UQZGC402_chkPrinter", value="0")
        
        # 11 バッチ起動画面: 「処理開始」ボタン押下
        # ジョブ実行(実行した日時を保持しておく)
        exec_datetime = self.exec_batch_job()
        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        
        # 12 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.screen_shot("バッチ起動画面_12")
        
        # 13 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        # 処理が終わるまで待機する
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        
        # 14 ジョブ実行履歴画面: 「検索」ボタン押下
        #self.find_element(By.ID,"SearchButton").click()
        
        # 15 ジョブ実行履歴画面: 表示
        #self.screen_shot("ジョブ実行履歴画面_15")
        
        # 16 ジョブ実行履歴画面: No.1 高額対象者計算処理_総合支援_事前処理  の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_16")
        
        # 17 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        #self.click_report_log()
        # 今回の処理で作成したPDFのDL（戻値はDLしたファイル数）
        #report_dl_count = self.get_job_report_pdf(exec_datetime=exec_datetime)
        self.return_click()
        
        # 18 ジョブ帳票履歴画面: 表示
        #self.screen_shot("ジョブ帳票履歴画面_18")
        
        # 19 ジョブ帳票履歴画面: 「検索」ボタン押下
        #self.find_element(By.ID,"SearchButton").click()
        
        # 20 ジョブ帳票履歴画面: 表示
        #self.screen_shot("ジョブ帳票履歴画面_20")
        
        # 21 ジョブ帳票履歴画面: 新高額障害福祉サービス等給付費対象者一覧　のNoボタンを押下
        # self.find_element(By.ID,"Sel1").click()
        
        # 22 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        #self.screen_shot("帳票（PDF）_22")
        
        # 23 帳票（PDF）: 表示
        #self.screen_shot("帳票（PDF）_23")
        
        # 24 帳票（PDF）: ×ボタン押下でPDFを閉じる
        #self.screen_shot("帳票（PDF）_24")
        
        # 25 ジョブ帳票履歴画面: 新高額障害福祉サービス等給付費支給期間5年未満対象者一覧　のNoボタンを押下
        # self.find_element(By.ID,"Sel2").click()
        
        # 26 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        #self.screen_shot("帳票（PDF）_26")
        
        # 27 帳票（PDF）: 表示
        #self.screen_shot("帳票（PDF）_27")
        
        # 28 帳票（PDF）: ×ボタン押下でPDFを閉じる
        #self.screen_shot("帳票（PDF）_28")
        
        # 29 ジョブ帳票履歴画面: 新高額障害福祉サービス等給付費対象者一覧(所得要確認)　のNoボタンを押下
        # self.find_element(By.ID,"Sel3").click()
        
        # 30 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        #self.screen_shot("帳票（PDF）_30")
        
        # 31 帳票（PDF）: 表示
        #self.screen_shot("帳票（PDF）_31")
        
        # 32 帳票（PDF）: ×ボタン押下でPDFを閉じる
        #self.screen_shot("帳票（PDF）_32")
        
        # 33 ジョブ帳票履歴画面: 新高額障害福祉サービス等給付費支給期間5年未満対象者一覧(所得要確認)　のNoボタンを押下
        # self.find_element(By.ID,"Sel4").click()
        
        # 34 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        #self.screen_shot("帳票（PDF）_34")
        
        # 35 帳票（PDF）: 表示
        #self.screen_shot("帳票（PDF）_35")
        
        # 36 帳票（PDF）: ×ボタン押下でPDFを閉じる
        #self.screen_shot("帳票（PDF）_36")
        
        # 37 ジョブ帳票履歴画面: 「戻る」ボタン押下
        #self.find_element(By.ID,"GOBACK").click()
        
        # 38 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_38")
        
