from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28030502(KodomoSiteTestCaseBase):
    """TestQAP010_28030502"""

    def setUp(self):
        super().setUp()

    # 副食費免除、非免除にかかわる対象者の抽出を行えることを確認する。
    def test_QAP010_28030502(self):
        """対象者抽出"""

        case_data_029_ZEAF000400 = self.test_data["029_ZEAF000400"]
        case_data_030_ZEAF000400 = self.test_data["030_ZEAF000400"]
        case_data_031_ZEAF002200 = self.test_data["031_ZEAF002200"]
        case_data_041_ZEAF000400 = self.test_data["041_ZEAF000400"]
        case_data_042_ZEAF002200 = self.test_data["042_ZEAF002200"]
        case_data_047_ZEAF000400 = self.test_data["047_ZEAF000400"]
        case_data_048_ZEAF000400 = self.test_data["048_ZEAF000400"]
        case_data_049_ZEAF002200 = self.test_data["049_ZEAF002200"]
        case_data_059_ZEAF000400 = self.test_data["059_ZEAF000400"]
        case_data_060_ZEAF002200 = self.test_data["060_ZEAF002200"]
        case_data_073_ZEAF000400 = self.test_data["073_ZEAF000400"]
        case_data_074_ZEAF000400 = self.test_data["074_ZEAF000400"]
        case_data_075_ZEAF002200 = self.test_data["075_ZEAF002200"]
        case_data_085_ZEAF000400 = self.test_data["085_ZEAF000400"]
        case_data_086_ZEAF002200 = self.test_data["086_ZEAF002200"]
        tab_index = 0

        self.do_login_new_tab()

        # 25 メインメニュー 画面: 「バッチ管理」ボタン押下
        # 26 メインメニュー 画面: 「即時実行」ボタンを押下
        # 27 メインメニュー 画面: 「スケジュール個別追加」ボタン タブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=True)
        tab_index += 1

        # 28 スケジュール個別追加 画面：子ども子育て支援 サブシステム名：賦課 処理名：月次賦課処理
        # 29 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_029_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_029_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_029_ZEAF000400.get("shori_mei", ""), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_29")

        # 30 スケジュール個別追加 画面: 「整合性チェック」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_030_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 31 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_031_ZEAF002200.get("shokanku", "")},
            {"title": "支所", "type": "select", "value": case_data_031_ZEAF002200.get("shisho", "")},
            {"title": "開始年月", "type": "text", "value": case_data_031_ZEAF002200.get("kaishi_nengetsu", "")},
            {"title": "終了年月", "type": "text", "value": case_data_031_ZEAF002200.get("shuryo_nengetsu", "")},
            {"title": "チェック項目設定", "type": "select",
             "value": case_data_031_ZEAF002200.get("chekku_komoku_settei", "")},
            {"title": "並び順", "type": "select", "value": case_data_031_ZEAF002200.get("naribi_jun", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)

        self.screen_shot("実行指示 画面_31")

        # 32 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 33 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_33")

        # 34 実行指示 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 35 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_35")

        # 36 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 37 ファイルダウンロード 画面:「No1」ボタン押下
        # 38 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 39 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 40 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_40")

        # 41 スケジュール個別追加 画面: 「月次賦課計算処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_041_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)
        self.screen_shot("実行指示 画面_41")

        # 42 実行指示 画面: パラメータを入力
        params = [
            {"title": "対象年月", "type": "text", "value": case_data_042_ZEAF002200.get("taisho_nengetsu", "")},
            {"title": "対象年度", "type": "text", "value": case_data_042_ZEAF002200.get("taisho_nendo", "")},
            {"title": "基準年月", "type": "text", "value": case_data_042_ZEAF002200.get("kijun_nengetsu", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_42")

        # 43 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 44 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_44")

        # 45 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002300", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_45")

        # 46 スケジュール個別追加 画面：子ども子育て支援 サブシステム名：入所 処理名：食材料費徴収者確認リスト出力
        # 47 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_047_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_047_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_047_ZEAF000400.get("shori_mei", ""), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_47")

        # 48 スケジュール個別追加 画面: 「食材料費徴収者確認リストデータ作成」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_048_ZEAF000400.get("batch_job_003", ""),
                                             tab_index=tab_index)
        self.screen_shot("実行指示 画面_48")

        # 49 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_049_ZEAF002200.get("shokanku", "")},
            {"title": "施設種類", "type": "select", "value": case_data_049_ZEAF002200.get("shisetsu_shurui", "")},
            {"title": "公私区分", "type": "select", "value": case_data_049_ZEAF002200.get("koshi_kubun", "")},
            {"title": "施設コード", "type": "text", "value": case_data_049_ZEAF002200.get("shisetsu_kodo", "")},
            {"title": "対象年月開始", "type": "text", "value": case_data_049_ZEAF002200.get("taisho_nengetsu_kaishi", "")},
            {"title": "対象年月終了", "type": "text", "value": case_data_049_ZEAF002200.get("taisho_nengetsu_shuryo", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_49")

        # 50 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 51 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_51")

        # 52 実行指示 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 53 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_53")

        # 54 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 55 ファイルダウンロード 画面:「No1」ボタン押下
        # 56 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 57 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 58 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_58")

        # 59 スケジュール個別追加 画面: 「食材料費徴収者確認リスト出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_059_ZEAF000400.get("batch_job_004", ""),
                                             tab_index=tab_index)

        # 60 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_060_ZEAF002200.get("shokanku", "")},
            {"title": "施設コード", "type": "text", "value": case_data_060_ZEAF002200.get("shisetsu_kodo", "")},
            {"title": "出力対象", "type": "select", "value": case_data_060_ZEAF002200.get("shutsuryoku_taisho", "")},
            {"title": "出力対象者", "type": "select",
             "value": case_data_060_ZEAF002200.get("shutsuryoku_taishosha", "")},
            {"title": "用途", "type": "select", "value": case_data_060_ZEAF002200.get("yoto", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_60")

        # 61 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 62 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_62")

        # 63 実行指示 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 64 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_64")

        # 65 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 66 ファイルダウンロード 画面:「No1」ボタン押下
        # 67 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 68 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 69 メインメニュー 画面: 「バッチ管理」ボタン押下
        # 70 メインメニュー 画面: 「即時実行」ボタンを押下
        # 71 メインメニュー 画面: 「スケジュール個別追加」ボタン タブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加")
        tab_index += 1

        # 72 スケジュール個別追加 画面：子ども子育て支援 サブシステム名：入所 処理名：食材料費減免対象者リスト出力
        # 73 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_073_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_073_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_073_ZEAF000400.get("shori_mei", ""), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_73")

        # 74 スケジュール個別追加 画面: 「食材料費減免対象者リストデータ作成」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_074_ZEAF000400.get("batch_job_005", ""),
                                             tab_index=tab_index)
        self.screen_shot("実行指示 画面_74")

        # 75 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_075_ZEAF002200.get("shokanku", "")},
            {"title": "施設種類", "type": "select", "value": case_data_075_ZEAF002200.get("shisetsu_shurui", "")},
            {"title": "公私区分", "type": "select", "value": case_data_075_ZEAF002200.get("koshi_kubun", "")},
            {"title": "施設コード", "type": "text", "value": case_data_075_ZEAF002200.get("shisetsu_kodo", "")},
            {"title": "対象年月開始", "type": "text", "value": case_data_075_ZEAF002200.get("taisho_nengetsu_kaishi", "")},
            {"title": "対象年月終了", "type": "text", "value": case_data_075_ZEAF002200.get("taisho_nengetsu_shuryo", "")},
            {"title": "対象施設", "type": "select", "value": case_data_075_ZEAF002200.get("taisho_shisetsu", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_75")

        # 76 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 77 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_77")

        # 78 実行指示 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 79 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_79")

        # 80 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 81 ファイルダウンロード 画面:「No1」ボタン押下
        # 82 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 83 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 84 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_84")

        # 85 スケジュール個別追加 画面: 「食材料費減免対象者リスト出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_085_ZEAF000400.get("batch_job_006", ""),
                                             tab_index=tab_index)
        self.screen_shot("実行指示 画面_85")

        # 86 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_086_ZEAF002200.get("shokanku", "")},
            {"title": "施設コード", "type": "text", "value": case_data_086_ZEAF002200.get("shisetsu_kodo", "")},
            {"title": "出力対象", "type": "select", "value": case_data_086_ZEAF002200.get("shutsuryoku_taisho", "")},
            {"title": "用途", "type": "select", "value": case_data_086_ZEAF002200.get("yoto", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_86")

        # 87 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 88 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_88")

        # 89 実行指示 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 90 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_90")

        # 91 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 92 ファイルダウンロード 画面:「No1」ボタン押下
        # 93 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 94 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
