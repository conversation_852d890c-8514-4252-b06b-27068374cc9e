from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28030503(KodomoSiteTestCaseBase):
    """TestQAP010_28030503"""

    def setUp(self):
        super().setUp()

    # 副食費免除、非免除にかかわる各種帳票の出力ができることを確認する。
    def test_QAP010_28030503(self):
        """各種帳票作成"""

        case_data_99_QAZF100001 = self.test_data["99_QAZF100001"]
        case_data_100_QAZF100002 = self.test_data["100_QAZF100002"]
        case_data_102_QAPF100300 = self.test_data["102_QAPF100300"]
        case_data_108_QAPF900200 = self.test_data["108_QAPF900200"]
        case_data_118_ZEAF000400 = self.test_data["118_ZEAF000400"]
        case_data_119_ZEAF000400 = self.test_data["119_ZEAF000400"]
        case_data_120_ZEAF002200 = self.test_data["120_ZEAF002200"]
        case_data_130_ZEAF000400 = self.test_data["130_ZEAF000400"]
        case_data_131_ZEAF002200 = self.test_data["131_ZEAF002200"]
        tab_index = 0

        self.do_login_new_tab()

        # 96 メインメニュー 画面: 「子ども子育て支援」ボタン押下
        # 97 メインメニュー 画面: 「世帯情報」ボタン押下
        # 98 メインメニュー 画面: 「検索」ボタンをダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="世帯情報", menu_level_3="検索",
                                 is_new_tab=True)
        tab_index += 1

        # 99 検索条件入力 画面: 入力できること
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
            value=case_data_99_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
            text=case_data_99_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
            value=case_data_99_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
            text=case_data_99_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
            value=case_data_99_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
            value=case_data_99_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
            text=case_data_99_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
            text=case_data_99_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
            value=case_data_99_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
            value=case_data_99_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
            value=case_data_99_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
            value=case_data_99_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
            value=case_data_99_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
            text=case_data_99_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
            value=case_data_99_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
            value=case_data_99_QAZF100001.get("denwa_bangou_ichi", ""))

        self.screen_shot("検索条件入力 画面_99")

        # 100 検索条件入力 画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_100_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code, tab_index)

        # 101 世帯履歴 画面: 対象の「№」ボタン押下
        self.click_button_by_label("1")
        self.screen_shot("世帯台帳 画面_101")

        # 102 世帯台帳 画面: 対象児童の「No.」ボタン押下
        self.click_kodomo_by_atena_code(case_data_102_QAPF100300.get("kodomo_atena_code", ""))

        # 103 児童台帳 画面: 「入所管理」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF103500", label="入所管理")
        self.screen_shot("児童台帳 画面_103")

        # 104 入所管理（申込） 画面: 「入所管理」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103500_btnNyushoKanri_button")
        self.screen_shot("入所管理（申込）画面_104")

        # 105 入所管理（申込） 画面: 「印刷」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103700_printbtn_button")

        # 106 印刷指示画面 画面: 「食材料費徴収通知書_新」のチェックボックスにチェックを入れる
        # 107 印刷指示画面 画面:「食材料費徴収通知書_新」タブ押下
        # 108 印刷指示画面 画面:「対象年月」「出力区分」「免除項目」「発行年月日」を入力します
        exec_params = [
            {
                "report_name": case_data_108_QAPF900200.get("chouhyou_mei_1", ""),
                "params_1": [
                    {"title": "発行場所", "value": case_data_108_QAPF900200.get("hakkou_basho_1", "")},
                    {"title": "トレイ", "value": case_data_108_QAPF900200.get("torei_1", "")},
                    {"title": "部数", "value": case_data_108_QAPF900200.get("busuu_1", "")}
                ],

                "params_2": [
                    {"title": "対象年月", "value": case_data_108_QAPF900200.get("taisho_nengetsu", "")},
                    {"title": "出力区分", "value": case_data_108_QAPF900200.get("shutsuryoku_kubun", "")},
                    {"title": "免除項目", "value": case_data_108_QAPF900200.get("menjo_komoku", "")},
                    {"title": "発行年月日", "value": case_data_108_QAPF900200.get("hakko_nengetsuhi", "")}
                ],
            },
        ]

        checked_reports_count = self.select_kodomo_report(report_param_list=exec_params, tab_index=tab_index,
                                                          screen_shot_name="印刷指示 画面_108",
                                                          screen_id="QAPF900200")

        # 109 納品物管理画面 画面:「印刷」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF900200_printBtn_button")

        # 110 印刷指示画面 画面:「印刷してよろしいですか？」に対し「はい」ボタン押下
        self.alert_accept()

        # 111 ファイルダウンロード 画面: 対象の帳票の「No.」ボタン押下
        # 112 ファイルダウンロード 画面:
        self.print_kodomo_reports(limit_wait_count=20, time_span_sec=4, tab_index=tab_index,
                                  screen_shot_name="ファイルダウンロード 画面_111", count_report=checked_reports_count)

        # 113 印刷指示画面 画面: 「閉じる」ボタン押下
        self.click_button_by_label("閉じる")

        # 114 メインメニュー 画面: 「バッチ管理」ボタン押下
        # 115 メインメニュー 画面: 「即時実行」ボタンを押下
        # 116 メインメニュー 画面: 「スケジュール個別追加」ボタン タブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加")
        tab_index += 1

        # 117 スケジュール個別追加 画面：子ども子育て支援 サブシステム名：入所 処理名：食材料費徴収免除(取消)通知書出力_新
        # 118 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_118_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_118_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_118_ZEAF000400.get("shori_mei", ""), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_118")

        # 119 スケジュール個別追加 画面: 「食材料費徴収免除（取消）通知書　抽出の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_119_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)
        self.screen_shot("実行指示 画面_119")

        # 120 実行指示 画面: パラメータを入力
        params = [
            {"title": "対象年月", "type": "text", "value": case_data_120_ZEAF002200.get("taisho_nengetsu", "")},
            {"title": "出力区分", "type": "select", "value": case_data_120_ZEAF002200.get("shutsuryoku_kubun", "")},
            {"title": "免除項目", "type": "select", "value": case_data_120_ZEAF002200.get("menjo_komoku", "")},
            {"title": "再発行区分", "type": "select", "value": case_data_120_ZEAF002200.get("saihakko_kubun", "")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_120_ZEAF002200.get("yubin_kunai_tokubetsu_umu", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_120_ZEAF002200.get("hakko_nengetsuhi", "")},
            {"title": "並び順", "type": "select", "value": case_data_120_ZEAF002200.get("narabi_jun", "")},
            {"title": "所管区", "type": "select", "value": case_data_120_ZEAF002200.get("shokanku", "")},
            {"title": "支所", "type": "select", "value": case_data_120_ZEAF002200.get("shisho", "")},
            {"title": "施設種類", "type": "select", "value": case_data_120_ZEAF002200.get("shisetsu_shurui", "")},
            {"title": "入所形態", "type": "select", "value": case_data_120_ZEAF002200.get("nyusho_keitai", "")},
            {"title": "実施区分", "type": "select", "value": case_data_120_ZEAF002200.get("jisshi_kubun", "")},
            {"title": "公私区分", "type": "select", "value": case_data_120_ZEAF002200.get("koshi_kubun", "")},
            {"title": "施設徴収有無", "type": "select",
             "value": case_data_120_ZEAF002200.get("shisetsu_choshu_umu", "")},
            {"title": "施設コード", "type": "text", "value": case_data_120_ZEAF002200.get("shisetsu_kodo", "")},
            {"title": "児童宛名コード", "type": "text", "value": case_data_120_ZEAF002200.get("jido_atena_kodo", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_120")

        # 121 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 122 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_122")

        # 123 実行指示 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 124 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_124")

        # 125 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 126 ファイルダウンロード 画面:「No1」ボタン押下
        # 127 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 128 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 129 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_129")

        # 130 スケジュール個別追加 画面: 「食材料費徴収免除（取消）通知書　出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_130_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)

        # 131 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_131_ZEAF002200.get("shokanku", "")},
            {"title": "支所", "type": "select", "value": case_data_131_ZEAF002200.get("shisho", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_131_ZEAF002200.get("hakko_nengetsuhi", "")},
            {"title": "出力帳票", "type": "select", "value": case_data_131_ZEAF002200.get("shutsuryoku_chohyou", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_131")

        # 132 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 133 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_133")

        # 134 実行指示 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 135 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_135")

        # 136 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 137 ファイルダウンロード 画面:「No1」ボタン押下
        # 138 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 139 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
