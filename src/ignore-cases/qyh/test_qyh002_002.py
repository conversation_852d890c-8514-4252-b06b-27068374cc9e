import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQYH002002(FukushiSiteTestCaseBase):
    """TESTQYH002002"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QYH002-002.sql", params=atena_list)
        super().setUp()
    
    def test_case_qyh002_002(self):
        """test_case_qyh002_002"""
        driver = None
        test_data = self.common_test_data
        
        self.do_login()

        #メインメニューボタン押下
        self.find_element(By.ID,"CmdProcess20_1").click()
        self.save_screenshot_migrate(driver, "QYH002-002-001", True)
        self.find_element(By.ID,"span_CmdButton30").click()
        self.save_screenshot_migrate(driver, "QYH002-002-002", True)
        self.find_element(By.ID,"TxtYochienKanaMeisho").send_keys(u"てすと")
        self.save_screenshot_migrate(driver, "QYH002-002-003", True)
        self.find_element(By.ID,"span_CmdKensaku").click()
        self.save_screenshot_migrate(driver, "QYH002-002-004", True)