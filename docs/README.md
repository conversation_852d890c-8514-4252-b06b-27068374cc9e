# ドキュメントの更新

このドキュメントサイトのメンテの仕方です。

[Vuepress](https://v2.vuepress.vuejs.org/) を利用しています。

## 資源

リポジトリ内の`docs`ディレクトリがドキュメントの資源です。

## ドキュメントサイトの修正の仕方

まず、nodeをインストールして、ローカル環境でnodeを利用できるようにしてください。

nodeのバージョンは最低限v14以降です。

初回のみ下記セットアップを行います。

```bash
cd docs
npm ci
```

ローカルで動作させる場合は下記です。

```bash
npm run dev
```

ホットリロードに対応した簡易サーバが起動しますので、こちらで確認しながら修正を行います。

修正が終わったら下記でビルドします。

```bash
npm run build
```

ビルドすると下記に資源ができます。このディレクトリをサーバにそのままコピーします。

```
docs/src/.vuepress/dist
```

公開サーバは現状は下記です。

```
HostName ************
User ec2-user
Port 22
```

SCPでコピーする場合は以下の様にします。

```bash
scp -r docs/src/.vuepress/dist ************:/home/<USER>/
```

上記でサーバ上のユーザホームにdistディレクトリがコピーされます。

サーバ上の配置場所は下記です。

```
/home/<USER>/apps/wyvern-doc/html
```

Webサーバはnginxをdocker-composeで動作させています。

資源を反映させる場合はサーバ上で下記の要領で行ってください。

```bash
# ドキュメントサイトのディレクトリに移動
cd apps/wyvern-doc
# サービスを停止する
docker-compose stop
# ドキュメントのディレクトリを削除
rm -rf html
# コピーした資源をhtmlという名称のディレクトリでコピーする
mv ~/dist ./html
# サービス起動
docker-compose up -d
```
